#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
WebSocket回调处理器模块
提供可扩展的回调处理机制
"""

import time
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Callable, Optional, Any
from traceback import format_exc

from .WebSocketCore import OrderbookData, OrderbookCache


class BaseCallbackHandler(ABC):
    """WebSocket回调处理器基类"""
    
    def __init__(self, orderbook_cache: OrderbookCache):
        self.orderbook_cache = orderbook_cache
        self.counter = 0
        
    @abstractmethod
    def handle_orderbook_update(self, adapter, data: dict):
        """处理订单簿更新的抽象方法"""
        pass
    
    def _extract_exchange_name(self, adapter) -> str:
        """从适配器中提取交易所名称"""
        exchange_name = getattr(adapter, '_exchange_name', 'unknown')
        if hasattr(adapter, '__class__'):
            class_name = adapter.__class__.__name__
            if 'Binance' in class_name:
                exchange_name = 'binance'
            elif 'Hyperliquid' in class_name:
                exchange_name = 'hyperliquid'
            elif 'Bybit' in class_name:
                exchange_name = 'bybit'
            elif 'Okx' in class_name:
                exchange_name = 'okx'
            elif 'Gate' in class_name:
                exchange_name = 'gateio'
            elif 'Bitget' in class_name:
                exchange_name = 'bitget'
            elif 'Backpack' in class_name:
                exchange_name = 'backpack'
        return exchange_name
    
    def _parse_orderbook_data(self, data: dict) -> Optional[OrderbookData]:
        """解析订单簿数据"""
        try:
            bid_price = float(data.get('bid1_price', 0))
            bid_amount = float(data.get('bid1_amount', 0))
            ask_price = float(data.get('ask1_price', 0))
            ask_amount = float(data.get('ask1_amount', 0))
            
            orderbook_data = OrderbookData(
                bid_price=bid_price,
                bid_amount=bid_amount,
                ask_price=ask_price,
                ask_amount=ask_amount,
                timestamp=time.time()
            )
            
            return orderbook_data if orderbook_data.is_valid() else None
        except (ValueError, TypeError) as e:
            print(f"Error parsing orderbook data: {e}")
            return None


class ArbitrageCallbackHandler(BaseCallbackHandler):
    """套利交易回调处理器"""
    
    def __init__(self, 
                 orderbook_cache: OrderbookCache,
                 exchange_1_name: str,
                 exchange_2_name: str,
                 symbol: str,
                 r_threshold: float,
                 trading_enabled_callback: Callable[[], bool],
                 trigger_trading_callback: Callable[[], None],
                 trade_cooldown: float = 1.0):
        super().__init__(orderbook_cache)
        self.exchange_1_name = exchange_1_name
        self.exchange_2_name = exchange_2_name
        self.symbol = symbol
        self.r_threshold = r_threshold
        self.trading_enabled_callback = trading_enabled_callback
        self.trigger_trading_callback = trigger_trading_callback
        self.trade_cooldown = trade_cooldown
        self.last_trade_time = 0
        self.last_print_time = 0  # 添加上次打印时间记录
        self.last_alert_time = 0  # 添加上次警告时间记录
        
    def handle_orderbook_update(self, adapter, data: dict):
        """处理订单簿更新"""
        try:
            # 确定交易所名称
            exchange_name = self._extract_exchange_name(adapter)
            
            if data.get('type') != 'best_orderbook':
                print(f"[{exchange_name}] 收到非订单簿数据: {data.get('type', 'unknown_type')}")
                return
            
            # 解析订单簿数据
            orderbook_data = self._parse_orderbook_data(data)
            if not orderbook_data:
                print(f"[{exchange_name}] 无效的订单簿数据: {data}")
                return
            
            # 注释掉，避免刷屏
            # # 显示接收到的数据
            # print(f"[{exchange_name}] 订单簿更新: 买1={orderbook_data.bid_price:.6f}({orderbook_data.bid_amount:.2f}) "
            #       f"卖1={orderbook_data.ask_price:.6f}({orderbook_data.ask_amount:.2f})")
            
            # 更新缓存
            self.orderbook_cache.update(exchange_name, orderbook_data)
            
            # 计算溢价
            self._calculate_premium()
            
        except Exception as e:
            print(f"Error in orderbook callback: {e}")
            print(format_exc())
    
    def _calculate_premium(self):
        """计算溢价并检查是否触发交易"""
        if not self.orderbook_cache.has_both_exchanges():
            self._show_waiting_message()
            return
        
        exchange_1_data = self.orderbook_cache.get(self.exchange_1_name)
        exchange_2_data = self.orderbook_cache.get(self.exchange_2_name)
        
        if not exchange_1_data or not exchange_2_data:
            return
        
        # 计算溢价 (exchange_2买1价格 / exchange_1卖1价格 - 1)
        premium = (exchange_2_data.bid_price / exchange_1_data.ask_price - 1)

        # 每0.1秒打印一次溢价信息
        current_time = time.time()
        if current_time - self.last_print_time >= 0.1:
            print(f"💰 {self.symbol} 溢价计算: {self.exchange_1_name}卖1={exchange_1_data.ask_price:.6f}, "
                  f"{self.exchange_2_name}买1={exchange_2_data.bid_price:.6f}, 溢价={premium:.4f}")
            self.last_print_time = current_time
        
        # 检查是否超过阈值
        if abs(premium) >= self.r_threshold:
            # 每0.2秒打印一次警告信息，避免刷屏
            if current_time - self.last_alert_time >= 0.2:
                print(f"🚨 溢价超过阈值! 当前溢价={premium:.4f}, 阈值={self.r_threshold:.4f}")
                self.last_alert_time = current_time

            # 检查是否可以触发交易逻辑（复用之前的current_time）
            if (self.trading_enabled_callback() and
                current_time - self.last_trade_time >= self.trade_cooldown):

                # 异步触发交易逻辑
                asyncio.create_task(self.trigger_trading_callback())
                self.last_trade_time = current_time
    
    def _show_waiting_message(self):
        """显示等待消息（避免刷屏）"""
        self.counter += 1
        if self.counter % 10 == 0:
            exchange_1_data = self.orderbook_cache.get(self.exchange_1_name)
            exchange_2_data = self.orderbook_cache.get(self.exchange_2_name)
            
            if exchange_1_data and not exchange_2_data:
                print(f"⚠️  等待{self.exchange_2_name}数据... (已有{self.exchange_1_name}数据)")
            elif exchange_2_data and not exchange_1_data:
                print(f"⚠️  等待{self.exchange_1_name}数据... (已有{self.exchange_2_name}数据)")
            else:
                print(f"⚠️  等待两个交易所的数据...")


class ReverseArbitrageCallbackHandler(BaseCallbackHandler):
    """反向套利交易回调处理器（用于sell_swap_buy_swap_wss.py）"""

    def __init__(self,
                 orderbook_cache: OrderbookCache,
                 exchange_1_name: str,
                 exchange_2_name: str,
                 symbol: str,
                 r_threshold: float,
                 trading_enabled_callback: Callable[[], bool],
                 trigger_trading_callback: Callable[[], None],
                 trade_cooldown: float = 1.0):
        super().__init__(orderbook_cache)
        self.exchange_1_name = exchange_1_name
        self.exchange_2_name = exchange_2_name
        self.symbol = symbol
        self.r_threshold = r_threshold
        self.trading_enabled_callback = trading_enabled_callback
        self.trigger_trading_callback = trigger_trading_callback
        self.trade_cooldown = trade_cooldown
        self.last_trade_time = 0
        self.last_print_time = 0  # 添加上次打印时间记录
        self.last_alert_time = 0  # 添加上次警告时间记录

    def handle_orderbook_update(self, adapter, data: dict):
        """处理订单簿更新"""
        try:
            # 确定交易所名称
            exchange_name = self._extract_exchange_name(adapter)

            if data.get('type') != 'best_orderbook':
                print(f"[{exchange_name}] 收到非订单簿数据: {data.get('type', 'unknown_type')}")
                return

            # 解析订单簿数据
            orderbook_data = self._parse_orderbook_data(data)
            if not orderbook_data:
                print(f"[{exchange_name}] 无效的订单簿数据: {data}")
                return

            # 更新缓存
            self.orderbook_cache.update(exchange_name, orderbook_data)

            # 计算溢价（反向逻辑）
            self._calculate_premium_reverse()

        except Exception as e:
            print(f"Error in reverse arbitrage callback: {e}")
            print(format_exc())

    def _calculate_premium_reverse(self):
        """计算溢价并检查是否触发交易（反向逻辑：exchange_1买1 / exchange_2卖1 - 1）"""
        if not self.orderbook_cache.has_both_exchanges():
            self._show_waiting_message()
            return

        exchange_1_data = self.orderbook_cache.get(self.exchange_1_name)
        exchange_2_data = self.orderbook_cache.get(self.exchange_2_name)

        if not exchange_1_data or not exchange_2_data:
            return

        # 计算溢价 (exchange_1买1价格 / exchange_2卖1价格 - 1) - 反向逻辑
        premium = (exchange_1_data.bid_price / exchange_2_data.ask_price - 1)

        # 每0.1秒打印一次溢价信息
        current_time = time.time()
        if current_time - self.last_print_time >= 0.1:
            print(f"💰 {self.symbol} 溢价计算: {self.exchange_1_name}买1={exchange_1_data.bid_price:.6f}, "
                  f"{self.exchange_2_name}卖1={exchange_2_data.ask_price:.6f}, 溢价={premium:.4f}")
            self.last_print_time = current_time

        # 检查是否超过阈值
        if abs(premium) >= self.r_threshold:
            # 每0.2秒打印一次警告信息，避免刷屏
            if current_time - self.last_alert_time >= 0.2:
                print(f"🚨 溢价超过阈值! 当前溢价={premium:.4f}, 阈值={self.r_threshold:.4f}")
                self.last_alert_time = current_time

            # 检查是否可以触发交易逻辑（复用之前的current_time）
            if (self.trading_enabled_callback() and
                current_time - self.last_trade_time >= self.trade_cooldown):

                # 异步触发交易逻辑
                asyncio.create_task(self.trigger_trading_callback())
                self.last_trade_time = current_time

    def _show_waiting_message(self):
        """显示等待消息（避免刷屏）"""
        self.counter += 1
        if self.counter % 10 == 0:
            exchange_1_data = self.orderbook_cache.get(self.exchange_1_name)
            exchange_2_data = self.orderbook_cache.get(self.exchange_2_name)

            if exchange_1_data and not exchange_2_data:
                print(f"⚠️  等待{self.exchange_2_name}数据... (已有{self.exchange_1_name}数据)")
            elif exchange_2_data and not exchange_1_data:
                print(f"⚠️  等待{self.exchange_1_name}数据... (已有{self.exchange_2_name}数据)")
            else:
                print(f"⚠️  等待两个交易所的数据...")


class SimpleDisplayCallbackHandler(BaseCallbackHandler):
    """简单显示回调处理器（仅显示数据，不进行交易）"""

    def handle_orderbook_update(self, adapter, data: dict):
        """处理订单簿更新"""
        try:
            exchange_name = self._extract_exchange_name(adapter)

            if data.get('type') != 'best_orderbook':
                return

            orderbook_data = self._parse_orderbook_data(data)
            if not orderbook_data:
                return

            print(f"[{exchange_name}] 买1={orderbook_data.bid_price:.6f}({orderbook_data.bid_amount:.2f}) "
                  f"卖1={orderbook_data.ask_price:.6f}({orderbook_data.ask_amount:.2f})")

            self.orderbook_cache.update(exchange_name, orderbook_data)

        except Exception as e:
            print(f"Error in display callback: {e}")


def create_callback_handler(handler_type: str,
                          orderbook_cache: OrderbookCache,
                          **kwargs) -> BaseCallbackHandler:
    """回调处理器工厂函数"""
    if handler_type == 'arbitrage':
        return ArbitrageCallbackHandler(orderbook_cache, **kwargs)
    elif handler_type == 'reverse_arbitrage':
        return ReverseArbitrageCallbackHandler(orderbook_cache, **kwargs)
    elif handler_type == 'display':
        return SimpleDisplayCallbackHandler(orderbook_cache)
    else:
        raise ValueError(f"Unsupported handler type: {handler_type}")
